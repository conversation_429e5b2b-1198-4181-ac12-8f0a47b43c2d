import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, CommonModule],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App {
  protected title = '1st-Monthsary-Tangi';

  // Floating hearts data
  protected floatingHearts = [
    { id: 1, left: 10, top: 20, size: 20, delay: 0 },
    { id: 2, left: 80, top: 10, size: 25, delay: 1 },
    { id: 3, left: 60, top: 70, size: 18, delay: 2 },
    { id: 4, left: 30, top: 50, size: 22, delay: 0.5 },
    { id: 5, left: 90, top: 80, size: 20, delay: 1.5 },
    { id: 6, left: 15, top: 90, size: 24, delay: 2.5 },
    { id: 7, left: 70, top: 30, size: 19, delay: 3 },
    { id: 8, left: 45, top: 15, size: 21, delay: 0.8 }
  ];

  // Story milestones data
  protected storyMilestones = [
    {
      id: 1,
      emoji: '👀',
      title: 'First Glance',
      description: 'The moment our eyes met and the world seemed to pause. I knew something special was about to begin.',
      date: 'Day 1'
    },
    {
      id: 2,
      emoji: '💬',
      title: 'First Conversation',
      description: 'Hours flew by like minutes as we talked about everything and nothing. Your laugh became my favorite sound.',
      date: 'Day 3'
    },
    {
      id: 3,
      emoji: '📱',
      title: 'First Call',
      description: 'Staying up until 3 AM just to hear your voice. Distance meant nothing when we were talking.',
      date: 'Day 7'
    },
    {
      id: 4,
      emoji: '💕',
      title: 'First "I Love You"',
      description: 'Three words that changed everything. The moment I knew my heart belonged to you completely.',
      date: 'Day 15'
    },
    {
      id: 5,
      emoji: '🎉',
      title: 'Officially Together',
      description: 'The day we decided to write our love story together. Best decision I ever made.',
      date: 'Day 20'
    }
  ];

  // Love notes data
  protected loveNotes = [
    {
      id: 1,
      emoji: '😍',
      title: 'Your Smile',
      message: 'Your smile lights up my entire world and makes even the darkest days feel bright and beautiful.'
    },
    {
      id: 2,
      emoji: '💖',
      title: 'Your Kindness',
      message: 'The way you care for others and show compassion makes me fall in love with you more every day.'
    },
    {
      id: 3,
      emoji: '🌟',
      title: 'Your Intelligence',
      message: 'Your brilliant mind and the way you see the world never fails to amaze and inspire me.'
    },
    {
      id: 4,
      emoji: '🎵',
      title: 'Your Laugh',
      message: 'Your laughter is my favorite melody, and I would do anything just to hear it every day.'
    },
    {
      id: 5,
      emoji: '🤗',
      title: 'Your Hugs',
      message: 'In your arms, I have found my home, my peace, and my greatest comfort in this world.'
    },
    {
      id: 6,
      emoji: '✨',
      title: 'Your Dreams',
      message: 'The passion you have for your dreams and goals motivates me to be the best version of myself.'
    }
  ];

  // Photo gallery data
  protected photoGallery = [
    {
      id: 1,
      emoji: '📸',
      caption: 'Our first selfie together'
    },
    {
      id: 2,
      emoji: '🌅',
      caption: 'Watching the sunrise, thinking of you'
    },
    {
      id: 3,
      emoji: '🍕',
      caption: 'Our first virtual dinner date'
    },
    {
      id: 4,
      emoji: '🎮',
      caption: 'Gaming together until 3 AM'
    },
    {
      id: 5,
      emoji: '💌',
      caption: 'The love letter that started it all'
    },
    {
      id: 6,
      emoji: '🌙',
      caption: 'Good night texts every single day'
    }
  ];

  // Promises data
  protected promises = [
    {
      id: 1,
      title: 'Always Listen',
      text: 'I promise to always listen to you with my heart, not just my ears.'
    },
    {
      id: 2,
      title: 'Support Your Dreams',
      text: 'I will always be your biggest cheerleader and support your goals.'
    },
    {
      id: 3,
      title: 'Make You Laugh',
      text: 'I promise to find new ways to make you smile every single day.'
    },
    {
      id: 4,
      title: 'Be Patient',
      text: 'I will be patient with you, especially when you are not patient with yourself.'
    },
    {
      id: 5,
      title: 'Love You More',
      text: 'I promise to love you more today than yesterday, but less than tomorrow.'
    },
    {
      id: 6,
      title: 'Never Stop Trying',
      text: 'I will never stop trying to be the best boyfriend you deserve.'
    }
  ];

  // Memory jar data
  protected memoryJar = Array.from({ length: 30 }, (_, i) => ({
    id: i + 1,
    day: i + 1,
    unlocked: i < 5, // First 5 days unlocked for demo
    showMessage: false,
    message: `Day ${i + 1}: Another beautiful day of loving you! 💖`
  }));

  // Surprise state
  protected surpriseTriggered = false;
  protected confettiPieces = [
    { id: 1, left: 10, top: 10, delay: 0, color: '#ff6b9d', shape: '🎉' },
    { id: 2, left: 20, top: 15, delay: 0.2, color: '#c44569', shape: '✨' },
    { id: 3, left: 80, top: 20, delay: 0.4, color: '#f8b500', shape: '🎊' },
    { id: 4, left: 70, top: 25, delay: 0.6, color: '#6c5ce7', shape: '💖' },
    { id: 5, left: 30, top: 30, delay: 0.8, color: '#a29bfe', shape: '🌟' },
    { id: 6, left: 90, top: 35, delay: 1, color: '#fd79a8', shape: '💫' }
  ];

  // Scroll to section method
  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  }

  // Toggle memory method
  toggleMemory(dayId: number) {
    const day = this.memoryJar.find(d => d.id === dayId);
    if (day && day.unlocked) {
      day.showMessage = !day.showMessage;
      // Hide other messages
      this.memoryJar.forEach(d => {
        if (d.id !== dayId) d.showMessage = false;
      });
    }
  }

  // Trigger surprise method
  triggerSurprise() {
    this.surpriseTriggered = true;
  }
}
