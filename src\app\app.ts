import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, CommonModule],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App {
  protected title = '1st-Monthsary-Tangi';

  // Floating hearts data
  protected floatingHearts = [
    { id: 1, left: 10, top: 20, size: 20, delay: 0 },
    { id: 2, left: 80, top: 10, size: 25, delay: 1 },
    { id: 3, left: 60, top: 70, size: 18, delay: 2 },
    { id: 4, left: 30, top: 50, size: 22, delay: 0.5 },
    { id: 5, left: 90, top: 80, size: 20, delay: 1.5 },
    { id: 6, left: 15, top: 90, size: 24, delay: 2.5 },
    { id: 7, left: 70, top: 30, size: 19, delay: 3 },
    { id: 8, left: 45, top: 15, size: 21, delay: 0.8 }
  ];
}
