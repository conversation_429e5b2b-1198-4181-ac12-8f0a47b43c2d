<!-- 💖 Monthsary Website - A Digital Love Letter 💖 -->

<!-- Floating Hearts Background -->
<div class="fixed inset-0 pointer-events-none overflow-hidden z-0">
  @for (heart of floatingHearts; track heart.id) {
    <div
      class="absolute text-pink-300 opacity-20 floating-heart"
      [style.left.%]="heart.left"
      [style.top.%]="heart.top"
      [style.animation-delay]="heart.delay + 's'"
      [style.font-size.px]="heart.size">
      💖
    </div>
  }
</div>

<!-- Main Container -->
<div class="relative z-10 min-h-screen">

  <!-- 1. Intro Splash Section -->
  <section class="min-h-screen flex items-center justify-center relative overflow-hidden">
    <div class="absolute inset-0 romantic-gradient opacity-30"></div>
    <div class="text-center z-10 px-4 fade-in-up">
      <h1 class="font-dancing text-6xl md:text-8xl text-romantic mb-6 heartbeat">
        Happy 1st Monthsary
      </h1>
      <h2 class="font-playfair text-3xl md:text-5xl text-gray-700 mb-8 italic">
        My Love 💖
      </h2>
      <p class="font-poppins text-lg md:text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
        Welcome to our digital love story, where every pixel is painted with affection
        and every line of code whispers "I love you"
      </p>
      <div class="mt-12">
        <button
          (click)="scrollToSection('our-story')"
          class="bg-gradient-to-r from-pink-400 to-purple-400 text-white px-8 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300">
          Begin Our Journey 💕
        </button>
      </div>
    </div>

    <!-- Sparkle Effects -->
    <div class="absolute top-20 left-20 text-yellow-300 text-2xl animate-pulse">✨</div>
    <div class="absolute top-40 right-32 text-pink-300 text-xl animate-bounce">💫</div>
    <div class="absolute bottom-32 left-40 text-purple-300 text-3xl animate-pulse">⭐</div>
    <div class="absolute bottom-20 right-20 text-yellow-300 text-xl animate-bounce">✨</div>
  </section>

  <!-- 2. Our Story Timeline -->
  <section id="our-story" class="py-20 px-4 bg-gradient-to-br from-pink-50 to-purple-50">
    <div class="max-w-6xl mx-auto">
      <h2 class="font-playfair text-4xl md:text-6xl text-center text-romantic mb-16">
        Our Beautiful Story
      </h2>

      <div class="relative">
        <!-- Timeline Line -->
        <div class="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-pink-300 to-purple-300 rounded-full"></div>

        <!-- Timeline Items -->
        <div class="space-y-16">
          @for (milestone of storyMilestones; track milestone.id; let i = $index) {
            <div class="flex items-center" [class.flex-row-reverse]="i % 2 === 1">
              <div class="w-1/2 px-8">
                <div class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 border border-pink-100">
                  <div class="flex items-center mb-4">
                    <span class="text-3xl mr-3">{{ milestone.emoji }}</span>
                    <h3 class="font-playfair text-xl font-semibold text-gray-800">{{ milestone.title }}</h3>
                  </div>
                  <p class="font-poppins text-gray-600 leading-relaxed">{{ milestone.description }}</p>
                  <span class="inline-block mt-3 text-sm font-medium text-pink-500 bg-pink-50 px-3 py-1 rounded-full">
                    {{ milestone.date }}
                  </span>
                </div>
              </div>

              <!-- Timeline Dot -->
              <div class="w-6 h-6 bg-gradient-to-r from-pink-400 to-purple-400 rounded-full border-4 border-white shadow-lg z-10"></div>

              <div class="w-1/2"></div>
            </div>
          }
        </div>
      </div>
    </div>
  </section>

  <!-- 3. What I Love About You -->
  <section class="py-20 px-4 bg-gradient-to-br from-purple-50 to-pink-50">
    <div class="max-w-6xl mx-auto">
      <h2 class="font-playfair text-4xl md:text-6xl text-center text-romantic mb-16">
        What I Love About You
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @for (loveNote of loveNotes; track loveNote.id) {
          <div class="group">
            <div class="bg-gradient-to-br from-pink-200 to-purple-200 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div class="text-center">
                <span class="text-4xl mb-4 block">{{ loveNote.emoji }}</span>
                <h3 class="font-playfair text-xl font-semibold text-gray-800 mb-4">{{ loveNote.title }}</h3>
                <p class="font-poppins text-gray-700 leading-relaxed">{{ loveNote.message }}</p>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  </section>

  <!-- 4. Final Message -->
  <section class="py-20 px-4 bg-gradient-to-br from-pink-100 to-purple-100">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="font-playfair text-4xl md:text-6xl text-romantic mb-8">
        This is just the beginning...
      </h2>
      <div class="bg-white rounded-2xl p-8 shadow-2xl border border-pink-200">
        <p class="font-poppins text-xl text-gray-700 leading-relaxed mb-6">
          Happy 1st Monthsary, my love! 💖
        </p>
        <p class="font-poppins text-lg text-gray-600 leading-relaxed">
          Every day with you feels like a new adventure, and I can't wait to see what
          amazing memories we'll create together. Here's to many more months, years,
          and a lifetime of love, laughter, and beautiful moments.
        </p>
      </div>
    </div>
  </section>

</div>

<!-- Footer -->
<footer class="bg-gradient-to-r from-pink-400 to-purple-400 text-white py-8 text-center">
  <p class="font-dancing text-2xl mb-2">Made with 💖 by Your Coding Boyfriend</p>
  <p class="font-poppins text-sm opacity-80">Happy 1st Monthsary, My Love!</p>
</footer>
    <div class="max-w-6xl mx-auto">
      <h2 class="font-playfair text-4xl md:text-6xl text-center text-romantic mb-16">
        Our Precious Memories
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @for (photo of photoGallery; track photo.id) {
          <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
            <div class="bg-white p-4 pb-8 transform rotate-1 group-hover:rotate-0 transition-transform duration-300">
              <div class="w-full h-64 bg-gradient-to-br from-pink-100 to-purple-100 rounded-lg flex items-center justify-center mb-4">
                <span class="text-6xl">{{ photo.emoji }}</span>
              </div>
              <p class="font-dancing text-lg text-gray-700 text-center">{{ photo.caption }}</p>
            </div>
          </div>
        }
      </div>
    </div>
  </section>

  <!-- 5. Coder Boyfriend Letter -->
  <section class="py-20 px-4 bg-gradient-to-br from-gray-900 to-purple-900 text-white">
    <div class="max-w-4xl mx-auto">
      <h2 class="font-playfair text-4xl md:text-6xl text-center text-pink-300 mb-16">
        A Letter From Your Coder Boyfriend
      </h2>

      <!-- Code Editor Style Box -->
      <div class="bg-gray-800 rounded-lg overflow-hidden shadow-2xl mb-12">
        <div class="bg-gray-700 px-4 py-2 flex items-center space-x-2">
          <div class="w-3 h-3 bg-red-500 rounded-full"></div>
          <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
          <span class="ml-4 text-gray-300 text-sm font-mono">love_letter.js</span>
        </div>
        <div class="p-6 font-mono text-sm">
          <div class="text-gray-400">// My feelings for you, compiled with love 💖</div>
          <div class="mt-2">
            <span class="text-purple-400">const</span>
            <span class="text-blue-300"> myLove</span>
            <span class="text-white"> = </span>
            <span class="text-green-300">"infinite"</span><span class="text-white">;</span>
          </div>
          <div class="mt-1">
            <span class="text-purple-400">const</span>
            <span class="text-blue-300"> feelings</span>
            <span class="text-white"> = [</span><span class="text-green-300">"love"</span><span class="text-white">, </span><span class="text-green-300">"gratitude"</span><span class="text-white">, </span><span class="text-green-300">"happiness"</span><span class="text-white">];</span>
          </div>
          <div class="mt-2">
            <span class="text-purple-400">while</span><span class="text-white"> (</span><span class="text-blue-300">true</span><span class="text-white">) {</span>
          </div>
          <div class="ml-4 mt-1">
            <span class="text-blue-300">console</span><span class="text-white">.</span><span class="text-yellow-300">log</span><span class="text-white">(</span><span class="text-green-300">"I love you more each day ❤️"</span><span class="text-white">);</span>
          </div>
          <div class="mt-1">
            <span class="text-white">}</span>
          </div>
          <div class="mt-2 text-gray-400">// This loop will run forever, just like my love for you</div>
        </div>
      </div>

      <!-- Beautiful Love Letter -->
      <div class="bg-white text-gray-800 rounded-2xl p-8 shadow-2xl">
        <h3 class="font-playfair text-2xl text-romantic mb-6 text-center">My Dearest Love,</h3>
        <div class="font-poppins text-lg leading-relaxed space-y-4">
          <p>
            As I write this code and build this website for you, every line reminds me of how you've
            transformed my world. Just like how I debug code with patience and care, you've helped me
            debug my heart and find the love I never knew I was missing.
          </p>
          <p>
            You are my favorite algorithm - complex, beautiful, and perfectly designed. Every day with
            you is like discovering a new feature that makes everything work better. You've optimized
            my happiness and eliminated all the bugs in my lonely heart.
          </p>
          <p>
            This website is more than just HTML, CSS, and JavaScript - it's a digital love letter,
            a virtual hug, and a promise that no matter how many lines of code I write in my lifetime,
            none will ever be as important as the story we're writing together.
          </p>
          <p class="text-center font-dancing text-xl text-romantic">
            Forever yours,<br>
            Your Coding Boyfriend 💻💖
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- 6. Our Song Section -->
  <section class="py-20 px-4 bg-gradient-to-br from-purple-50 to-pink-50">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="font-playfair text-4xl md:text-6xl text-romantic mb-8">
        Our Song 🎵
      </h2>
      <p class="font-poppins text-lg text-gray-600 mb-12">
        The melody that plays in my heart every time I think of you
      </p>

      <!-- Music Player Placeholder -->
      <div class="bg-white rounded-2xl p-8 shadow-lg border border-pink-100 mb-8">
        <div class="flex items-center justify-center space-x-4 mb-6">
          <div class="w-16 h-16 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full flex items-center justify-center">
            <span class="text-white text-2xl">🎵</span>
          </div>
          <div class="text-left">
            <h3 class="font-playfair text-xl font-semibold text-gray-800">Perfect by Ed Sheeran</h3>
            <p class="text-gray-600">Our special song</p>
          </div>
        </div>

        <!-- Placeholder for music player -->
        <div class="bg-gray-100 rounded-lg p-6 text-center">
          <p class="text-gray-600 mb-4">🎶 Music Player Placeholder 🎶</p>
          <p class="text-sm text-gray-500">
            Replace this section with your favorite music player embed<br>
            (Spotify, YouTube, Apple Music, etc.)
          </p>
        </div>
      </div>

      <!-- Animated Music Notes -->
      <div class="flex justify-center space-x-8">
        <span class="text-3xl text-pink-400 animate-bounce" style="animation-delay: 0s;">🎵</span>
        <span class="text-2xl text-purple-400 animate-bounce" style="animation-delay: 0.2s;">🎶</span>
        <span class="text-3xl text-pink-400 animate-bounce" style="animation-delay: 0.4s;">🎵</span>
        <span class="text-2xl text-purple-400 animate-bounce" style="animation-delay: 0.6s;">🎶</span>
      </div>
    </div>
  </section>

  <!-- 7. Promise Wall -->
  <section class="py-20 px-4 bg-gradient-to-br from-pink-50 to-purple-50">
    <div class="max-w-6xl mx-auto">
      <h2 class="font-playfair text-4xl md:text-6xl text-center text-romantic mb-16">
        My Promises to You
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        @for (promiseItem of promises; track promiseItem.id) {
          <div class="group">
            <div class="bg-yellow-100 p-6 rounded-lg shadow-lg transform rotate-1 group-hover:rotate-0 group-hover:scale-105 transition-all duration-300 border-l-4 border-yellow-400">
              <div class="flex items-start space-x-3">
                <span class="text-2xl group-hover:animate-pulse">💖</span>
                <div>
                  <h3 class="font-playfair text-lg font-semibold text-gray-800 mb-2">{{ promiseItem.title }}</h3>
                  <p class="font-poppins text-gray-700 text-sm leading-relaxed">{{ promiseItem.text }}</p>
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  </section>

  <!-- 8. Daily Memory Jar -->
  <section class="py-20 px-4 bg-gradient-to-br from-purple-50 to-pink-50">
    <div class="max-w-6xl mx-auto">
      <h2 class="font-playfair text-4xl md:text-6xl text-center text-romantic mb-8">
        30 Days of Love
      </h2>
      <p class="font-poppins text-lg text-gray-600 text-center mb-16">
        Click on each heart to unlock a special memory from our journey together
      </p>

      <div class="grid grid-cols-5 md:grid-cols-6 lg:grid-cols-10 gap-4">
        @for (day of memoryJar; track day.id) {
          <div
            class="group cursor-pointer"
            (click)="toggleMemory(day.id)">
            <div class="relative">
              @if (day.unlocked) {
                <div class="w-12 h-12 bg-gradient-to-br from-pink-400 to-red-400 rounded-full flex items-center justify-center text-white text-xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  💖
                </div>
              } @else {
                <div class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 text-xl group-hover:scale-110 transition-transform duration-300">
                  🔒
                </div>
              }
              <span class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-gray-600 font-medium">{{ day.day }}</span>
            </div>

            @if (day.unlocked && day.showMessage) {
              <div class="absolute z-20 bg-white p-4 rounded-lg shadow-xl border border-pink-200 mt-2 w-64 transform -translate-x-1/2 left-1/2">
                <p class="text-sm text-gray-700 font-poppins">{{ day.message }}</p>
                <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white border-l border-t border-pink-200 rotate-45"></div>
              </div>
            }
          </div>
        }
      </div>
    </div>
  </section>

  <!-- 9. Final Surprise -->
  <section class="py-20 px-4 bg-gradient-to-br from-pink-100 to-purple-100 relative overflow-hidden">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="font-playfair text-4xl md:text-6xl text-romantic mb-8">
        One More Thing...
      </h2>

      <div class="mb-12">
        <button
          (click)="triggerSurprise()"
          class="group relative">
          <div class="w-32 h-32 bg-gradient-to-br from-pink-400 to-red-400 rounded-full flex items-center justify-center text-white text-6xl group-hover:scale-110 transition-all duration-300 shadow-2xl heartbeat">
            💖
          </div>
          <p class="font-dancing text-2xl text-gray-700 mt-4">Click Me!</p>
        </button>
      </div>

      @if (surpriseTriggered) {
        <div class="fade-in-up">
          <div class="bg-white rounded-2xl p-8 shadow-2xl border border-pink-200 mb-8">
            <h3 class="font-playfair text-3xl text-romantic mb-6">This is just Month One...</h3>
            <p class="font-poppins text-xl text-gray-700 leading-relaxed mb-6">
              Imagine what the future will be like with you. ❤️
            </p>
            <p class="font-poppins text-lg text-gray-600 leading-relaxed">
              Every day with you feels like a new adventure, and I can't wait to see what
              amazing memories we'll create together. Here's to many more months, years,
              and a lifetime of love, laughter, and beautiful moments.
            </p>
          </div>

          <!-- Confetti Animation -->
          <div class="fixed inset-0 pointer-events-none z-50">
            @for (confetti of confettiPieces; track confetti.id) {
              <div
                class="absolute animate-bounce"
                [style.left.%]="confetti.left"
                [style.top.%]="confetti.top"
                [style.animation-delay]="confetti.delay + 's'"
                [style.color]="confetti.color">
                {{ confetti.shape }}
              </div>
            }
          </div>
        </div>
      }
    </div>
  </section>

</div>

<!-- Footer -->
<footer class="bg-gradient-to-r from-pink-400 to-purple-400 text-white py-8 text-center">
  <p class="font-dancing text-2xl mb-2">Made with 💖 by Your Coding Boyfriend</p>
  <p class="font-poppins text-sm opacity-80">Happy 1st Monthsary, My Love!</p>
</footer>